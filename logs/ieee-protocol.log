2025-09-23T20:09:02.996+08:00  INFO 9004 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 9004 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol)
2025-09-23T20:09:02.998+08:00  INFO 9004 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-23T20:09:03.824+08:00  INFO 9004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-23T20:09:03.826+08:00  INFO 9004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-23T20:09:03.864+08:00  INFO 9004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-09-23T20:09:04.568+08:00  INFO 9004 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-23T20:09:04.576+08:00  INFO 9004 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-23T20:09:04.576+08:00  INFO 9004 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-23T20:09:04.640+08:00  INFO 9004 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-23T20:09:04.641+08:00  INFO 9004 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1593 ms
2025-09-23T20:09:05.752+08:00  INFO 9004 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-23T20:09:06.059+08:00  INFO 9004 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 13 endpoint(s) beneath base path '/actuator'
2025-09-23T20:09:06.105+08:00  INFO 9004 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-23T20:09:06.165+08:00  INFO 9004 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-23T20:09:06.165+08:00  INFO 9004 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-23T20:09:06.165+08:00  INFO 9004 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-23T20:09:06.167+08:00  INFO 9004 --- [main] c.w.ieee.config.GrpcServerConfiguration  : gRPC server configured to use virtual threads executor
2025-09-23T20:09:06.279+08:00  INFO 9004 --- [main] n.d.b.g.s.s.GrpcServerLifecycle          : gRPC Server started, listening on address: *, port: 9090
2025-09-23T20:09:06.290+08:00  INFO 9004 --- [main] c.w.ieee.IeeeServerApplication           : Started IeeeServerApplication in 3.652 seconds (process running for 4.373)
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#91]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#109]/runnable@ForkJoinPool-1-worker-2 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#111]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#99]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#112]/runnable@ForkJoinPool-1-worker-15 start
2025-09-23T20:09:06.299+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#80]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#117]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#118]/runnable@ForkJoinPool-1-worker-17 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#119]/runnable@ForkJoinPool-1-worker-11 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#120]/runnable@ForkJoinPool-1-worker-2 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#121]/runnable@ForkJoinPool-1-worker-16 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#123]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#125]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T20:09:06.299+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#78]/runnable@ForkJoinPool-1-worker-2 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#103]/runnable@ForkJoinPool-1-worker-13 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#128]/runnable@ForkJoinPool-1-worker-15 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#129]/runnable@ForkJoinPool-1-worker-9 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#130]/runnable@ForkJoinPool-1-worker-19 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#131]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T20:09:06.299+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#76]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#134]/runnable@ForkJoinPool-1-worker-11 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#135]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#137]/runnable@ForkJoinPool-1-worker-16 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#139]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#140]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#94]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#142]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#144]/runnable@ForkJoinPool-1-worker-13 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#145]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#146]/runnable@ForkJoinPool-1-worker-15 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#148]/runnable@ForkJoinPool-1-worker-19 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#147]/runnable@ForkJoinPool-1-worker-9 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#152]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T20:09:06.299+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#79]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#155]/runnable@ForkJoinPool-1-worker-2 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#156]/runnable@ForkJoinPool-1-worker-16 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#157]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#105]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#161]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#162]/runnable@ForkJoinPool-1-worker-13 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#163]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#164]/runnable@ForkJoinPool-1-worker-15 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#165]/runnable@ForkJoinPool-1-worker-19 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#167]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#166]/runnable@ForkJoinPool-1-worker-12 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#169]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#170]/runnable@ForkJoinPool-1-worker-9 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#172]/runnable@ForkJoinPool-1-worker-11 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#81]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#173]/runnable@ForkJoinPool-1-worker-2 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#174]/runnable@ForkJoinPool-1-worker-16 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#82]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#84]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T20:09:06.305+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#178]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#107]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#108]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#98]/runnable@ForkJoinPool-1-worker-11 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#110]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#83]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#87]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#85]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#86]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#90]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#88]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#92]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#93]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#89]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T20:09:06.300+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#95]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#96]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#97]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#100]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#101]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#106]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T20:09:06.301+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#104]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#115]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#114]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#122]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#124]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#127]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#132]/runnable@ForkJoinPool-1-worker-12 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#136]/runnable@ForkJoinPool-1-worker-2 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#138]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#116]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#149]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#102]/runnable@ForkJoinPool-1-worker-12 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#150]/runnable@ForkJoinPool-1-worker-12 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#153]/runnable@ForkJoinPool-1-worker-18 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#158]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#177]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#126]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#133]/runnable@ForkJoinPool-1-worker-17 start
2025-09-23T20:09:06.303+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#151]/runnable@ForkJoinPool-1-worker-17 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#154]/runnable@ForkJoinPool-1-worker-11 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#160]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#159]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#168]/runnable@ForkJoinPool-1-worker-17 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#175]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T20:09:06.302+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#113]/runnable@ForkJoinPool-1-worker-16 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#171]/runnable@ForkJoinPool-1-worker-18 start
2025-09-23T20:09:06.304+08:00  INFO 9004 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#176]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T20:09:06.809+08:00  INFO 9004 --- [RMI TCP Connection(3)-***********] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-23T20:09:06.809+08:00  INFO 9004 --- [RMI TCP Connection(3)-***********] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-23T20:09:06.811+08:00  INFO 9004 --- [RMI TCP Connection(3)-***********] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-23T20:09:06.828+08:00  INFO 9004 --- [RMI TCP Connection(4)-***********] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-09-23T20:09:14.672+08:00  INFO 9004 --- [SpringApplicationShutdownHook] n.d.b.g.s.s.GrpcServerLifecycle          : Completed gRPC server shutdown
2025-09-23T20:09:14.802+08:00  INFO 9004 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-09-23T20:09:56.839+08:00  INFO 14592 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 14592 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol)
2025-09-23T20:09:56.840+08:00  INFO 14592 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-23T20:09:57.671+08:00  INFO 14592 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-23T20:09:57.673+08:00  INFO 14592 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-23T20:09:57.716+08:00  INFO 14592 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-09-23T20:09:58.387+08:00  INFO 14592 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-23T20:09:58.394+08:00  INFO 14592 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-23T20:09:58.394+08:00  INFO 14592 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-23T20:09:58.460+08:00  INFO 14592 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-23T20:09:58.460+08:00  INFO 14592 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1576 ms
2025-09-23T20:09:59.594+08:00  INFO 14592 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-23T20:09:59.906+08:00  INFO 14592 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 13 endpoint(s) beneath base path '/actuator'
2025-09-23T20:09:59.969+08:00  INFO 14592 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-23T20:10:00.031+08:00  INFO 14592 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-23T20:10:00.031+08:00  INFO 14592 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-23T20:10:00.032+08:00  INFO 14592 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-23T20:10:00.033+08:00  INFO 14592 --- [main] c.w.ieee.config.GrpcServerConfiguration  : gRPC server configured to use virtual threads executor
2025-09-23T20:10:00.153+08:00  INFO 14592 --- [main] n.d.b.g.s.s.GrpcServerLifecycle          : gRPC Server started, listening on address: *, port: 9090
2025-09-23T20:10:00.164+08:00  INFO 14592 --- [main] c.w.ieee.IeeeServerApplication           : Started IeeeServerApplication in 3.66 seconds (process running for 4.29)
2025-09-23T20:10:00.748+08:00  INFO 14592 --- [RMI TCP Connection(1)-***********] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-23T20:10:00.748+08:00  INFO 14592 --- [RMI TCP Connection(1)-***********] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-23T20:10:00.750+08:00  INFO 14592 --- [RMI TCP Connection(1)-***********] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-23T20:10:00.768+08:00  INFO 14592 --- [RMI TCP Connection(2)-***********] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-09-23T20:10:27.483+08:00  INFO 14592 --- [SpringApplicationShutdownHook] n.d.b.g.s.s.GrpcServerLifecycle          : Completed gRPC server shutdown
2025-09-23T20:10:27.627+08:00  INFO 14592 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-09-23T20:10:48.349+08:00  INFO 19692 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 19692 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol)
2025-09-23T20:10:48.350+08:00  INFO 19692 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-23T20:10:49.165+08:00  INFO 19692 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-23T20:10:49.167+08:00  INFO 19692 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-23T20:10:49.201+08:00  INFO 19692 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-09-23T20:10:49.875+08:00  INFO 19692 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-23T20:10:49.882+08:00  INFO 19692 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-23T20:10:49.883+08:00  INFO 19692 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-23T20:10:49.944+08:00  INFO 19692 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-23T20:10:49.944+08:00  INFO 19692 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1548 ms
2025-09-23T20:10:51.050+08:00  INFO 19692 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-23T20:10:51.336+08:00  INFO 19692 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 13 endpoint(s) beneath base path '/actuator'
2025-09-23T20:10:51.379+08:00  INFO 19692 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-23T20:10:51.437+08:00  INFO 19692 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-23T20:10:51.437+08:00  INFO 19692 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-23T20:10:51.437+08:00  INFO 19692 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-23T20:10:51.438+08:00  INFO 19692 --- [main] c.w.ieee.config.GrpcServerConfiguration  : gRPC server configured to use virtual threads executor
2025-09-23T20:10:51.546+08:00  INFO 19692 --- [main] n.d.b.g.s.s.GrpcServerLifecycle          : gRPC Server started, listening on address: *, port: 9090
2025-09-23T20:10:51.556+08:00  INFO 19692 --- [main] c.w.ieee.IeeeServerApplication           : Started IeeeServerApplication in 3.533 seconds (process running for 4.174)
2025-09-23T20:10:52.254+08:00  INFO 19692 --- [RMI TCP Connection(3)-***********] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-23T20:10:52.254+08:00  INFO 19692 --- [RMI TCP Connection(3)-***********] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-23T20:10:52.256+08:00  INFO 19692 --- [RMI TCP Connection(3)-***********] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-23T20:10:52.274+08:00  INFO 19692 --- [RMI TCP Connection(2)-***********] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-09-23T20:12:31.049+08:00  INFO 19692 --- [SpringApplicationShutdownHook] n.d.b.g.s.s.GrpcServerLifecycle          : Completed gRPC server shutdown
2025-09-23T20:12:31.071+08:00 ERROR 19692 --- [ieee-schedule-2] c.w.i.c.ThreadPoolTaskExecutorConfig     : Task error

org.springframework.data.redis.RedisSystemException: Redis exception
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:72) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:38) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:256) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:969) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:826) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultManyInvocationSpec.toSet(LettuceInvoker.java:639) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$ManyInvocationSpec.toSet(LettuceInvoker.java:434) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:124) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$10(RedisTemplate.java:638) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.lambda$doWithKeys$22(RedisTemplate.java:785) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:406) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:373) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.doWithKeys(RedisTemplate.java:785) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:638) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at com.weihengtech.ieee.service.scheduler.RedisTaskSchedulerService.scanAndExecuteRedisTasks(RedisTaskSchedulerService.java:46) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-6.0.13.jar:6.0.13]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]
Caused by: io.lettuce.core.RedisException: Connection closed
	at io.lettuce.core.protocol.DefaultEndpoint.lambda$notifyDrainQueuedCommands$7(DefaultEndpoint.java:679) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.lettuce.core.protocol.DefaultEndpoint$Lazy.getNullable(DefaultEndpoint.java:1167) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.lettuce.core.protocol.DefaultEndpoint$Lazy.get(DefaultEndpoint.java:1152) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.lettuce.core.protocol.DefaultEndpoint.lambda$notifyDrainQueuedCommands$8(DefaultEndpoint.java:680) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.lettuce.core.protocol.DefaultEndpoint.cancelCommands(DefaultEndpoint.java:786) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.lettuce.core.protocol.DefaultEndpoint.notifyDrainQueuedCommands(DefaultEndpoint.java:680) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.lettuce.core.protocol.CommandHandler.channelInactive(CommandHandler.java:358) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.lettuce.core.protocol.RedisHandshakeHandler.channelInactive(RedisHandshakeHandler.java:94) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:305) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.lettuce.core.ChannelGroupListener.channelInactive(ChannelGroupListener.java:69) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:305) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:301) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe$7.run(AbstractChannel.java:813) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]
	... 1 common frames omitted

2025-09-23T20:12:31.190+08:00  INFO 19692 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-09-23T20:12:31.198+08:00  INFO 19692 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
